package com.angelalign.tas.solver;

import com.angelalign.tas.score.base.BaseConstraintProvider;
import com.angelalign.tas.score.china.ChinaDesignScheduleV2ConstraintProvider;
import com.angelalign.tas.score.china.ChinaRandomInspectionConstraintProvider;
import com.angelalign.tas.score.oversea.DefaultManualClaimHardConstraintProvider;
import com.angelalign.tas.score.oversea.runtime.OverSeaQualityInspectionRuntimeTaskConstraintProvider;
import com.angelalign.tas.score.oversea.runtime.OverSeaDdmRuntimeTaskConstraintProvider;
import com.angelalign.tas.score.oversea.runtime.OverSeaDesignRuntimeConstraintProvider;
import com.angelalign.tas.score.oversea.schedule.OverSeaDdmScheduleTaskHardConstraintProvider;
import com.angelalign.tas.score.oversea.schedule.OverSeaDesignScheduleTaskHardConstraintProvider;
import com.angelalign.tas.domain.solution.base.BaseSolution;
import com.angelalign.tas.domain.solution.china.ChinaDesignScheduleSolution;
import com.angelalign.tas.domain.solution.china.ChinaRandomInspectionRuntimeSolution;
import com.angelalign.tas.domain.solution.oversea.runtime.OverSeaQualityInspectionRuntimeSolution;
import com.angelalign.tas.domain.solution.oversea.schedule.OverSeaDdmScheduleSolution;
import com.angelalign.tas.domain.solution.oversea.schedule.OverSeaDesignScheduleSolution;
import com.angelalign.tas.domain.solution.oversea.DefaultManualClaimSolution;
import com.angelalign.tas.domain.solution.oversea.runtime.OverSeaDdmRuntimeSolution;
import com.angelalign.tas.domain.solution.oversea.runtime.OverSeaDesignRuntimeSolution;
import com.angelalign.tas.solver.phase.NoChangeCustomPhaseCommand;
import com.angelalign.tas.solver.phase.base.AbstractCustomPhaseCommand;
import com.angelalign.tas.solver.phase.FullDesignModifyTaskPhaseCommand;
import com.angelalign.tas.solver.phase.TaskPreferDesignerPhaseCommand;
import jodd.util.StringUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.List;
import java.util.stream.Stream;

@Getter
@AllArgsConstructor
public enum SolverTypeEnum {
    // 海外设计组实时分配
    OVERSEA_DESIGN_RUNTIME(OverSeaDesignRuntimeSolution.class
            , OverSeaDesignRuntimeConstraintProvider.class
            , List.of(
            FullDesignModifyTaskPhaseCommand.class
            , TaskPreferDesignerPhaseCommand.class)
            , OverSeaDesignRuntimeConstraintProvider.HARD_LEVEL
            , OverSeaDesignRuntimeConstraintProvider.SOFT_LEVEL),

    // 海外设计定时分配
    OVERSEA_DESIGN_SCHEDULE(OverSeaDesignScheduleSolution.class
            , OverSeaDesignScheduleTaskHardConstraintProvider.class
            , List.of(TaskPreferDesignerPhaseCommand.class)
            , OverSeaDesignScheduleTaskHardConstraintProvider.HARD_LEVEL
            , OverSeaDesignScheduleTaskHardConstraintProvider.SOFT_LEVEL),

    // 海外DDM定时分配
    OVERSEA_DDM_SCHEDULE(OverSeaDdmScheduleSolution.class
            , OverSeaDdmScheduleTaskHardConstraintProvider.class
            , List.of(TaskPreferDesignerPhaseCommand.class)
            , OverSeaDdmScheduleTaskHardConstraintProvider.HARD_LEVEL
            , OverSeaDdmScheduleTaskHardConstraintProvider.SOFT_LEVEL),

    // 海外DDM实时分配
    OVERSEA_DDM_RUNTIME(OverSeaDdmRuntimeSolution.class
            , OverSeaDdmRuntimeTaskConstraintProvider.class
            , List.of(TaskPreferDesignerPhaseCommand.class)
            , OverSeaDdmRuntimeTaskConstraintProvider.HARD_LEVEL
            , OverSeaDdmRuntimeTaskConstraintProvider.SOFT_LEVEL),

    // 通用质检组的实时分配
    COMMON_QUALITY_INSPECTION_RUNTIME(OverSeaQualityInspectionRuntimeSolution.class
            , OverSeaQualityInspectionRuntimeTaskConstraintProvider.class
            , OverSeaQualityInspectionRuntimeTaskConstraintProvider.HARD_LEVEL
            , OverSeaQualityInspectionRuntimeTaskConstraintProvider.SOFT_LEVEL),

    // 海外主动领取
    COMMON_MANUAL_CLAIM(DefaultManualClaimSolution.class
            , DefaultManualClaimHardConstraintProvider.class
            , List.of(TaskPreferDesignerPhaseCommand.class)
            , DefaultManualClaimHardConstraintProvider.HARD_LEVEL
            , DefaultManualClaimHardConstraintProvider.SOFT_LEVEL),

    // 国内的定时分配
    CHINA_DESIGN_SCHEDULE(ChinaDesignScheduleSolution.class
            , ChinaDesignScheduleV2ConstraintProvider.class
            , List.of(FullDesignModifyTaskPhaseCommand.class, TaskPreferDesignerPhaseCommand.class)
            , ChinaDesignScheduleV2ConstraintProvider.HARD_LEVEL
            , ChinaDesignScheduleV2ConstraintProvider.SOFT_LEVEL),
    CHINA_DESIGN_SCHEDULE_V2(ChinaDesignScheduleSolution.class
            , ChinaDesignScheduleV2ConstraintProvider.class
            , List.of(FullDesignModifyTaskPhaseCommand.class, TaskPreferDesignerPhaseCommand.class)
            , ChinaDesignScheduleV2ConstraintProvider.HARD_LEVEL
            , ChinaDesignScheduleV2ConstraintProvider.SOFT_LEVEL),
    // 国内质检工单的分配
    CHINA_RANDOM_INSPECTION_SCHEDULE(ChinaRandomInspectionRuntimeSolution.class
            , ChinaRandomInspectionConstraintProvider.class
            , ChinaRandomInspectionConstraintProvider.HARD_LEVEL
            , ChinaRandomInspectionConstraintProvider.SOFT_LEVEL);

    private final Class<? extends BaseSolution> solutionClass;
    private final Class<? extends BaseConstraintProvider> providerClass;
    private final List<Class<? extends AbstractCustomPhaseCommand>> customPhaseClass;
    private final int hardLevelSize;
    private final int softLevelSize;

    SolverTypeEnum(Class<? extends BaseSolution> solutionClass
            , Class<? extends BaseConstraintProvider> providerClass
            , int hardLevelSize, int softLevelSize) {
        this.softLevelSize = softLevelSize;
        this.hardLevelSize = hardLevelSize;
        this.providerClass = providerClass;
        this.solutionClass = solutionClass;
        this.customPhaseClass = List.of(NoChangeCustomPhaseCommand.class);
    }

    public static SolverTypeEnum findSolverByName(String solverName) {
        return findSolverByNameAndVersion(solverName, "");
    }

    public static SolverTypeEnum findSolverByNameAndVersion(String solverName, String version) {
        return Stream.of(SolverTypeEnum.values())
                .filter(solverTypeEnum -> solverTypeEnum.name()
                        .equalsIgnoreCase(StringUtil.isBlank(version) ? solverName : solverName + "_" + version))
                .findFirst().orElseThrow();
    }
}
