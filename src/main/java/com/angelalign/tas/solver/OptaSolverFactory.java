package com.angelalign.tas.solver;

import com.angelalign.tas.score.base.BaseConstraintProvider;
import com.angelalign.tas.domain.assignment.Problem;
import com.angelalign.tas.domain.solution.base.BaseSolution;
import com.angelalign.tas.solver.phase.base.AbstractCustomPhaseCommand;
import lombok.extern.slf4j.Slf4j;
import org.optaplanner.core.api.solver.SolverManager;
import org.optaplanner.core.config.solver.SolverConfig;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@Slf4j
public class OptaSolverFactory {
    private final OptaSolverConfigProvider optaSolverConfigProvider = new OptaSolverConfigProvider();

    public SolverManager<BaseSolution, Long> getSolverManager(Problem problem) {
        log.info("problem solverName {} version {}", problem.getSolverName(), problem.getVersion());

        SolverTypeEnum solverTypeEnum = SolverTypeEnum.findSolverByNameAndVersion(problem.getSolverName(), problem.getVersion());
        return createSolverManager(problem
                , solverTypeEnum.getSolutionClass()
                , solverTypeEnum.getProviderClass()
                , solverTypeEnum.getCustomPhaseClass()
                , solverTypeEnum.getHardLevelSize()
                , solverTypeEnum.getSoftLevelSize());
    }

    private SolverManager<BaseSolution, Long> createSolverManager(Problem problem
            , Class<? extends BaseSolution> solutionClass
            , Class<? extends BaseConstraintProvider> providerClass
            , List<Class<? extends AbstractCustomPhaseCommand>> customPhaseList
            , int hardLevelSize
            , int softLevelSize) {

        SolverConfig solverConfig = optaSolverConfigProvider.getDefaultSolverConfig(problem
                , solutionClass
                , providerClass
                , customPhaseList
                , hardLevelSize
                , softLevelSize);
        return SolverManager.create(solverConfig);
    }
}
