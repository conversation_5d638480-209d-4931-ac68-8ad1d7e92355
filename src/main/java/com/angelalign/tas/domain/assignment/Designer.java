package com.angelalign.tas.domain.assignment;

import com.angelalign.tas.score.base.util.DesignerCapacityConvertor;
import com.angelalign.tas.domain.assignment.enums.DesignerRankEnum;
import com.angelalign.tas.util.DecimalUtils;
import lombok.*;
import org.apache.commons.collections4.CollectionUtils;
import org.optaplanner.core.api.domain.entity.PlanningEntity;
import org.optaplanner.core.api.domain.lookup.PlanningId;
import org.optaplanner.core.api.domain.variable.InverseRelationShadowVariable;

import javax.persistence.*;
import java.util.*;

@PlanningEntity
@Entity(name = "ta_designer")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(of = {"id", "rank", "originId", "code"})
public class Designer implements Comparator<Designer> {
    @PlanningId
    @Id
    @GeneratedValue
    public Long id;
    @Column(name = "rank")
    @Enumerated(EnumType.STRING)
    public DesignerRankEnum rank;
    @ManyToOne
    @JoinColumn(name = "problem_id")
    public Problem problem;
    public String originId;
    public Boolean onDuty;
    public String code;
    public int designerLevelSequence;
    @ManyToMany(cascade = {CascadeType.PERSIST, CascadeType.MERGE})
    @JoinTable(
            name = "ta_rel_designer_skill", // 中间表的名称
            joinColumns = @JoinColumn(name = "designer_id"),
            inverseJoinColumns = @JoinColumn(name = "skill_id")
    )
    public Set<Skill> skills;
    @ElementCollection
    @CollectionTable(
            name = "ta_ref_designer_attributes",
            joinColumns = @JoinColumn(name = "designer_id")
    )
    @MapKeyColumn(name = "attribute_key")
    @Column(name = "attribute_value", length = 10000)
    @Builder.Default
    public Map<String, String> attributes = new HashMap<>();

    @OneToMany(mappedBy = "designer", cascade = CascadeType.ALL, orphanRemoval = true, fetch = FetchType.EAGER)
    @InverseRelationShadowVariable(sourceVariableName = "designer")
    public Set<Task> tasks;
    @OneToOne(mappedBy = "designer", cascade = CascadeType.ALL, orphanRemoval = true, fetch = FetchType.EAGER)
    public Capacity capacity;
    @OneToMany(mappedBy = "designer", cascade = CascadeType.ALL, orphanRemoval = true, fetch = FetchType.EAGER)
    @Builder.Default
    public Set<DesignerConsumedQuotaTask> designerConsumedQuotaTasks = Set.of();
    public int workTimeLimitationInMinutes;
    public int assignedWorkTimeInMinutes;

    public Set<DesignerConsumedQuotaTask> getDesignerConsumedQuotaTasks() {
        return Optional.ofNullable(designerConsumedQuotaTasks).orElse(Set.of());
    }

    public Designer id(final Long id) {
        this.setId(id);
        return this;
    }

    public Double getTotalConsumedQuota() {
        return DesignerCapacityConvertor.covertConsumedTaskQuota(this)
               + DesignerCapacityConvertor.covertAssignedTaskQuota(this);
    }

    public Double getOverAllocationLimit() {
        return Optional.ofNullable(getCapacity())
                .map(Capacity::getOverAllocationLimit)
                .map(Integer::doubleValue)
                .orElse(0d);
    }

    public Double getCapacityDailyTotalQuota() {
        return Optional.ofNullable(getCapacity())
                .map(Capacity::getOriginalQuota)
                .map(Integer::doubleValue)
                .orElse(Optional.ofNullable(getCapacity())
                        .map(Capacity::getCapacityTaskTypeQuotas)
                        .stream()
                        .flatMap(Collection::stream)
                        .filter(capacityTaskTypeQuota -> "equivalentTaskType".equals(capacityTaskTypeQuota.getTaskTypeCode()))
                        .mapToDouble(task -> (double) task.getQuota())
                        .sum());
    }

    public Double getAvailableQuota() {
        return Math.max(0, getOverAllocationLimit() - getTotalConsumedQuota());
    }

    @Override
    public String toString() {
        return getCode();
    }

    @Override
    public int compare(Designer o1, Designer o2) {
        return o2.getStrength() - o1.getStrength();
    }

    public int getStrength() {
        return DecimalUtils.preciseRound(getTotalConsumedQuota());
    }

    public Double getAppointedTaskQuota() {
        return getTasks()
                .stream()
                .filter(task -> CollectionUtils.isNotEmpty(task.getPreferredDesigner()))
                .filter(task -> task.getPreferredDesigner().stream().anyMatch(designer -> designer.getCode().equals(getCode())))
                .mapToDouble(task -> DesignerCapacityConvertor.covertAssignedTaskQuota(this, Set.of(task)))
                .sum();
    }
}
