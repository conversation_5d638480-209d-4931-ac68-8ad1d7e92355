package com.angelalign.tas.domain.assignment.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum EntityAttributeKey {
    PREFER_TASK("preferTask", AttributeKeyType.JSON, "偏好工单"),
    PREFER_SKILL("preferSkill", AttributeKeyType.LIST, "偏好技能"),
    IS_INTERN("isIntern", AttributeKeyType.BOOLEAN, "是否是实习生"),
    ORDER_TAG("orderTag", AttributeKeyType.LIST, "订单标签"),
    CASE_TAG("caseTag", AttributeKeyType.LIST, "病例标签"),
    TASK_TEAM("teamName", AttributeKeyType.STRING, "工单所属组"),
    DESIGNER_TEAM("teamName", AttributeKeyType.STRING, "设计师所属组"),
    PRODUCT_LINE("productLine", AttributeKeyType.STRING, "工单产品线"),
    PHASE_TYPE("phaseType", AttributeKeyType.STRING, "工单阶段"),



    TASK_REQUIRE_SKILL_CODE("taskRequireSkillCode", AttributeKeyType.LIST, "工单所有需要的技能");

    private final String key;
    private final AttributeKeyType type;
    private final String description;

    public enum AttributeKeyType {
        JSON, STRING, NUMBER, BOOLEAN, LIST
    }
}
