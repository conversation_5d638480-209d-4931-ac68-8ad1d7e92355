package com.angelalign.tas.domain.assignment;

import com.angelalign.tas.score.base.util.DesignerCapacityConvertor;
import com.angelalign.tas.domain.assignment.enums.PhaseType;
import com.angelalign.tas.domain.parser.TaskAttributeParser;
import com.angelalign.tas.rest.vo.support.MedTagVo;
import com.angelalign.tas.util.DecimalUtils;
import lombok.*;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;
import org.optaplanner.core.api.domain.entity.PlanningEntity;
import org.optaplanner.core.api.domain.lookup.PlanningId;
import org.optaplanner.core.api.domain.variable.PlanningVariable;

import javax.persistence.*;
import java.util.*;

@PlanningEntity(difficultyComparatorClass = Task.class)
@Entity(name = "ta_task")
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(of = {"id", "code", "caseCode"})
public class Task implements Comparator<Task> {

    @PlanningId
    @Id
    @GeneratedValue
    public Long id;
    @ManyToOne
    @JoinColumn(name = "problem_id")
    public Problem problem;
    @Column(name = "origin_id")
    public String originId;
    public String caseCode;
    public String code;
    @ManyToOne
    public TaskType taskType;
    @Enumerated(EnumType.STRING)
    public PhaseType phaseType;
    @ManyToMany(cascade = {CascadeType.PERSIST, CascadeType.MERGE})
    @JoinTable(
            name = "ta_rel_task_skill", // 中间表的名称
            joinColumns = @JoinColumn(name = "task_id"),
            inverseJoinColumns = @JoinColumn(name = "skill_id")
    )
    @Fetch(FetchMode.SUBSELECT)
    public Set<Skill> requiredSkill;
    public int baseDurationInMinutes;
    @Builder.Default
    public Float baseDurationInCount = 0f;
    @ManyToOne
    public Dentist dentist;
    @PlanningVariable(valueRangeProviderRefs = "designerRange", strengthComparatorClass = Designer.class)
    @ManyToOne
    public Designer designer;
    @ManyToMany(cascade = {CascadeType.PERSIST, CascadeType.MERGE}, fetch = FetchType.EAGER)
    @JoinTable(
            name = "ta_rel_task_designer_preference", // 中间表的名称
            joinColumns = @JoinColumn(name = "task_id"),
            inverseJoinColumns = @JoinColumn(name = "designer_id")
    )
    public Set<Designer> preferredDesigner;
    public Integer priorityScore;
    public String originalTag;
    @ElementCollection
    @CollectionTable(
            name = "ta_ref_task_attributes",
            joinColumns = @JoinColumn(name = "task_id")
    )
    @MapKeyColumn(name = "attribute_key")       // 定义 Map 的键列名
    @Column(name = "attribute_value", length = 10000 )    // 定义 Map 的值列名
    @Builder.Default
    public Map<String, String> attributes = new HashMap<>();
    public  Integer unassignedReason;

    public double getConsumeQuota() {
        return DesignerCapacityConvertor.covertAssignedTaskQuota(designer, Set.of(this));
    }

    public List<Skill> getMissingSkill() {
        return getRequiredSkill()
                .stream()
                .filter(skill -> !designer.getSkills().contains(skill)).toList();
    }

    @Override
    public String toString() {
        return getCode() + ":" + ((getDesigner() != null) ? getDesigner().toString() : "NOT ASSIGNED");
    }

    @Override
    public int compare(Task o1, Task o2) {
        return o1.getDifficulty() - o2.getDifficulty();
    }

    public int getDifficulty() {
        return requiredSkill.size()
               + TaskAttributeParser.getMegTag(this)
                       .stream()
                       .mapToInt(MedTagVo::getLevelScore)
                       .sum()
               + getPriorityScore()
               + preferredDesigner.size()
               + getBaseDurationInMinutes()
               + DecimalUtils.preciseRound(getBaseDurationInCount());
    }
}
