package com.angelalign.tas.persistence;

import com.angelalign.tas.domain.assignment.Problem;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface TaProblemRepository extends JpaRepository<Problem, Long> {

    @Override
    List<Problem> findAll();

    @Query(value = """
            SELECT
              id
            FROM
              ta_problem
            WHERE
              status=0
              AND call_back_url!=''
              AND call_back_url IS NOT NULL
            ORDER BY
              id asc
            LIMIT
              1
            """, nativeQuery = true)
    Long findLastProblem();
    @Query(value = "select  id  from ta_problem where status = 0  order by id asc ", nativeQuery = true)
    List<Long> findUnSolvedProblem();

    @Modifying
    @Query(value = "UPDATE ta_problem as p SET p.status  = 1 WHERE p.id  = :id", nativeQuery = true)
    void updateProblemStatusSolving(@Param("id") Long id);

    @Modifying
    @Query(value = "UPDATE ta_problem as p SET p.status  = 2 WHERE p.id  = :id", nativeQuery = true)
    void updateProblemStatusSolved(@Param("id") Long id);
}
