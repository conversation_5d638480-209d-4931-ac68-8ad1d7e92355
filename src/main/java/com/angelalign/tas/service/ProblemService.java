package com.angelalign.tas.service;

import com.angelalign.tas.common.callback.CallBackResultProxy;
import com.angelalign.tas.domain.assignment.*;
import com.angelalign.tas.domain.solution.base.BaseSolution;
import com.angelalign.tas.persistence.TaCaseTaskRepository;
import com.angelalign.tas.persistence.TaProblemRepository;
import com.angelalign.tas.persistence.TaTaskRepository;
import com.angelalign.tas.rest.vo.ProblemVo;
import com.angelalign.tas.rest.vo.SolutionVo;
import com.angelalign.tas.score.base.util.DesignerIdentityUtil;
import com.angelalign.tas.solver.OptaSolverFactory;
import com.angelalign.tas.util.Jackson;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

@Slf4j
@Service
@Transactional
public class ProblemService {
    @Autowired
    private OptaSolverFactory optaSolverFactory;
    @Autowired
    private TaProblemRepository problemRepository;
    @Autowired
    private CallBackResultProxy callBackResultProxy;
    @Autowired
    private TaCaseTaskRepository taCaseTaskRepository;
    @Autowired
    private TaTaskRepository taskRepository;

    public List<Problem> findByTaskId(String id) {
        return taskRepository.findByOriginId(id)
                .stream()
                .map(Task::getProblem)
                .toList();
    }

    @Transactional
    public long saveProblem(ProblemVo problemVo) {
        Problem problem = problemVo.convertToDomain();

        Problem savedProblem = problemRepository.save(problem);

        return savedProblem.getId();
    }

    public Problem findById(Long id) {
        return problemRepository.findById(id).orElseThrow();
    }

    public Optional<Problem> getProblemById(Long id) {
        Optional<Problem> result = problemRepository.findById(id);
        result.ifPresent(problem -> {
            AtomicInteger i = new AtomicInteger();
            log.info("problem found: {}", problem.getCode());
            log.info("problem tasks: {}", problem.getTasks().size());
            problem.getTasks().forEach(t -> {
                i.getAndAdd(t.getAttributes().size());
                i.getAndAdd(t.getRequiredSkill().size());
                i.getAndAdd(t.getPreferredDesigner().size());
                i.getAndAdd(t.getAttributes().size());
            });
            log.info("problem dentists: {}", problem.getDentists().size());
            problem.getDentists().forEach(d -> i.getAndAdd(d.getCooperatedDesigners().size() + d.getPreferredDesigners().size()));
            log.info("problem designers: {}", problem.getDesigners().size());
            problem.getDesigners().forEach(d -> {
                Capacity capacity = d.getCapacity();
                i.getAndAdd(d.getAttributes().size()
                            + d.getAttributes().size()
                            + d.getAttributes().size()
                            + d.getSkills().size()
                            + d.getTasks().size()
                            + d.getDesignerConsumedQuotaTasks().size()
                            + d.getDesignerConsumedQuotaTasks().stream().map(DesignerConsumedQuotaTask::getAttributes).toList().size()
                            + Optional.ofNullable(capacity).map(Capacity::getCapacityTaskTypeQuotas).map(Set::size).orElse(0));
            });
            i.getAndAdd(problem.getCaseTasks().size());
            log.info("problem task types: {}", problem.getTaskTypes().size());
            log.info("problem skills: {}", problem.getSkills().size());
            log.info("problem med tags: {}", problem.getMedTags().size());
            log.info("problem subtotal i: {}", i.get());
        });
        return result;
    }

    public void bestSolutionConsumer(BaseSolution solution) {
        log.info("best solution problem {} generated", solution.getProblem());
        log.info(Jackson.toJsonString(solution));
    }

    @Transactional(isolation = Isolation.READ_COMMITTED, propagation = Propagation.REQUIRES_NEW)
    public void finalBestSolutionHandeAndSaveConsumer(BaseSolution solution) {
        log.info("final solution problem {} score {}", solution.getProblem(), solution.getScore());
        Problem problem = problemRepository.findById(solution.getProblem().getId()).orElseThrow();
        // 总得分
        long[] hardScores = solution.getScore().hardScores();
        boolean match = Arrays.stream(hardScores).anyMatch(s -> s < 0);
        if (match) {
            log.info("final solution problem {} generated, but score is not match", solution.getProblem());
            solution.setAccepted(Boolean.FALSE);
            problem.setStatus(ProblemStatus.SOLVED);
            problem.setScore(solution.getScore().toString());
            problemRepository.save(problem);
            return;
        }
//        solution.getTaskList()
//                .stream()
//                .filter(task -> task.getDesigner() != null)
//                .forEach(task -> log.info("final result task [{}] -> designer [{}]", task.getCode(), task.getDesigner().getCode()));

        Map<Long, Task> taskMap = problem.getTasks().stream().collect(Collectors.toMap(Task::getId, t -> t));
        Map<Long, Designer> designerMap = problem.getDesigners().stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(Designer::getId, d -> d));
        for (Task task : solution.getTaskList()
                .stream()
                .filter(DesignerIdentityUtil::isAvailableDesigner)
                .toList()) {
            taskMap.get(task.getId()).setDesigner(designerMap.get(task.getDesigner().getId()));
            taskMap.get(task.getId()).setUnassignedReason(task.getUnassignedReason());
        }
        if (!ProblemStatus.SOLVED.equals(solution.getProblem().getStatus())) {
            callBackResult(problem, solution);
        } else {
            log.info("problem {} status is {} skip callback", problem.getId(), solution.getProblem().getStatus());
        }
        log.info("problem {} solve done!", problem.getId());
        problem.setScore(solution.getScore().toString());
        problem.setStatus(ProblemStatus.SOLVED);
        problemRepository.save(problem);
    }

    /**
     * 回调通知去更改任务的分配人
     *
     * @param problem 算出结果的问题
     */
    public void callBackResult(Problem problem, BaseSolution solution) {
        if (StringUtils.isBlank(problem.getCallBackUrl())) return;
        log.info("problem {} callBack result", problem.getId());
        SolutionVo solutionVo = SolutionVo.convertFrom(problem, solution);
        callBackResultProxy.callBack(problem.getCallBackUrl(), solutionVo);
    }
}
