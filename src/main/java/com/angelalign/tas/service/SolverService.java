package com.angelalign.tas.service;

import com.angelalign.tas.score.base.util.CaseTaskUtil;
import com.angelalign.tas.score.base.util.DesignerCapacityConvertor;
import com.angelalign.tas.score.base.util.DesignerPreferUtil;
import com.angelalign.tas.domain.assignment.Problem;
import com.angelalign.tas.domain.parser.ConsumedQuotaTaskAttributeParser;
import com.angelalign.tas.domain.parser.DesignerAttributeParser;
import com.angelalign.tas.domain.parser.TaskAttributeParser;
import com.angelalign.tas.domain.solution.base.BaseSolution;
import com.angelalign.tas.rest.vo.SolutionDetailVo;
import com.angelalign.tas.rest.vo.SolutionVo;
import com.angelalign.tas.score.base.util.ProblemUtil;
import com.angelalign.tas.solver.OptaSolverFactory;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.optaplanner.core.api.solver.SolverJob;
import org.optaplanner.core.api.solver.SolverManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.function.BiFunction;

@Service
@Slf4j
public class SolverService {
    @Autowired
    private ProblemService problemService;
    @Autowired
    private OptaSolverFactory optaSolverFactory;

    public SolutionVo blockSolve(Long problemId) {
        return solveInternal(problemId, SolutionVo::convertFrom);
    }

    public SolutionDetailVo blockSolveDetail(Long problemId) {
        return solveInternal(problemId, SolutionDetailVo::convertFrom);
    }

    @SneakyThrows
    private <T> T solveInternal(Long problemId, BiFunction<Problem, BaseSolution, T> converter) {
        Problem problem = problemService.getProblemById(problemId)
                .orElseThrow(() -> new IllegalArgumentException("Can't find problem by id [" + problemId + "]"));

        SolverManager<BaseSolution, Long> solverManager = optaSolverFactory.getSolverManager(problem);

        SolverJob<BaseSolution, Long> solve = solverManager.solve(
                problemId,
                (id) -> problem.convertToSolution(),
                problemService::finalBestSolutionHandeAndSaveConsumer,
                null
        );

        BaseSolution finalBestSolution = solve.getFinalBestSolution();
        boolean match = Arrays.stream(finalBestSolution.getScore().hardScores()).allMatch(s -> s >= 0);

        long count = match && finalBestSolution.getAccepted() ? finalBestSolution.getTaskList()
                .stream()
                .filter(task -> task.getDesigner() != null)
                .count() : 0;
        log.info("solve result count [{}]", count);

        close(solverManager);

        return converter.apply(problem, finalBestSolution);
    }

    private void close(SolverManager<BaseSolution, Long> solverManager) {
        solverManager.close();
        CaseTaskUtil.reset();
        DesignerCapacityConvertor.invalidateAll();
        DesignerPreferUtil.invalidateAll();
        ProblemUtil.invalidateAll();
        TaskAttributeParser.invalidateAll();
        ConsumedQuotaTaskAttributeParser.invalidateAll();
        DesignerAttributeParser.invalidateAll();
    }
}
