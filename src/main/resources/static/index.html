<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <title>TAS 工单查询系统</title>
    <style>
        /* 基础样式 */
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f0f2f5;
        }

        /* 环境选择器 */
        .env-selector {
            margin-bottom: 20px;
        }

        select {
            padding: 8px 12px;
            border-radius: 4px;
            border: 1px solid #ddd;
        }

        /* 查询表单 */
        #queryForm {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }

        #taskId {
            padding: 8px;
            width: 200px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }

        button {
            padding: 8px 16px;
            background: #1890ff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }

        /* 结果列表 */
        .task-item {
            background: white;
            padding: 15px;
            margin-bottom: 10px;
            border-radius: 4px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            cursor: pointer;
        }

        /* 抽屉样式 */
        .drawer {
            position: fixed;
            top: 0;
            right: -100%;
            width: 60%;
            height: 95%;
            background: white;
            box-shadow: -2px 0 8px rgba(0, 0, 0, 0.1);
            transition: right 0.3s;
            padding: 20px;
            overflow-y: auto;
        }

        .drawer.active {
            right: 0;
        }

        /* 新增样式，让抽屉内数据容器宽度为100% */
        /* 表格样式优化 */
        .task-table {
            border-collapse: collapse;
            width: 100%;
            margin-top: 10px;
        }

        .task-table th, .task-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }

        .task-table th {
            background-color: #f2f2f2;
            color: #333;
            font-weight: bold;
        }

        .task-table tr:hover {
            background-color: #f5f5f5;
        }

        #detailContent,
        .designer-list,
        .designer-card,
        .task-table {
            width: 100%;
        }
    </style>
</head>

<body>
<!-- 环境选择 -->
<div class="env-selector">
    <select id="environment">
        <option value="https://tas-sit.eainc.com">SIT环境</option>
        <option value="http://localhost:8080">本地环境</option>
        <option value="https://tas-cnsha.eainc.com">生产环境</option>
    </select>
</div>
<!-- 查询表单 -->
<form id="queryForm">
    <a>工单id</a>
    <input type="text" id="taskId" placeholder="输入工单ID（示例：335588）">
    <a>问题id</a>
    <input type="text" id="problemId" placeholder="输入问题ID">
    <button type="submit">查询</button>
</form>

<!-- 结果列表 -->
<div id="resultList"></div>

<!-- 抽屉面板 -->
<div class="drawer" id="drawer">
    <div class="drawer-header">
        <h2>分配详情</h2>
        <button onclick="closeDrawer()">关闭</button>
    </div>
    <div class="search-box">
        <input type="text" id="searchInput" placeholder="搜索设计师或任务编码" onkeyup="filterDetails()">
    </div>
    <div id="detailContent"></div>
</div>

<script>
    // 环境配置
    const ENV_MAP = {
        'SIT环境': 'https://tas-sit.eainc.com',
        '本地环境': 'http://localhost:8080',
        '生产环境': 'https://tas-cnsha.eainc.com'
    };

    // 页面加载时检查URL参数并自动查询
    window.addEventListener('DOMContentLoaded', function() {
        const urlParams = new URLSearchParams(window.location.search);
        const problemId = urlParams.get('problemId');
        const taskId = urlParams.get('taskId');

        if (problemId) {
            // 设置problemId输入框的值
            document.getElementById('problemId').value = problemId;
            // 自动触发查询
            setTimeout(() => {
                document.getElementById('queryForm').dispatchEvent(new Event('submit'));
            }, 100);
        } else if (taskId) {
            // 设置taskId输入框的值
            document.getElementById('taskId').value = taskId;
            // 自动触发查询
            setTimeout(() => {
                document.getElementById('queryForm').dispatchEvent(new Event('submit'));
            }, 100);
        }
    });

    // 查询功能
    document.getElementById('queryForm').addEventListener('submit', async (e) => {
        e.preventDefault();
        const problemId = document.getElementById('problemId').value;
        const taskId = problemId ? '' : document.getElementById('taskId').value;
        const baseUrl = document.getElementById('environment').value;

        if (problemId) {
            // 清空 taskId 输入框
            document.getElementById('taskId').value = '';
            try {
                // 显示加载状态
                document.getElementById('detailContent').innerHTML = '<p>加载中...</p>';
                openDrawer();

                const controller = new AbortController();
                const timeoutId = setTimeout(() => {
                    controller.abort();
                    document.getElementById('detailContent').innerHTML = '<p style="color:red">请求超时，请检查网络</p>';
                }, 1000000);

                const response = await fetch(`${baseUrl}/api/task-assignment/solution/detail/${problemId}`, { signal: controller.signal, method: 'GET', headers: { 'Content-Type': 'application/json' }, mode: 'cors' });
                clearTimeout(timeoutId);

                if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
                const { data } = await response.json();
                renderDetails(data);
            } catch (error) {
                document.getElementById('detailContent').innerHTML = `<p style="color:red">加载失败：${error.message}</p>`;
            }
        } else if (taskId && !problemId) {
            try {
                const response = await fetch(`${baseUrl}/api/task-assignment/findProblemByTaskId/${taskId}`, { method: 'GET', headers: { 'Content-Type': 'application/json' }, mode: 'cors' });
                const { data } = await response.json();
                renderResults(data);
            } catch (error) {
                alert('查询失败: ' + error.message);
            }
        }
    });

    // 渲染结果
    function renderResults(data) {
        const container = document.getElementById('resultList');
        container.innerHTML = data.map(item => `
            <div class="task-item" data-id="${item.id}">
                <h3>问题模型 ${item.code}</h3>
                <p>分配策略：${item.solverName}</p>
                <p>客户端：${item.client}</p>
                <p>评分：${item.score || '无'}</p>
            </div>
        `).join('');

        // 绑定点击事件
        document.querySelectorAll('.task-item').forEach(item => {
            item.addEventListener('click', async () => {
                const baseUrl = document.getElementById('environment').value;
                const taskId = item.dataset.id;
                const cacheKey = `task_${taskId}`;
                const cachedData = localStorage.getItem(cacheKey);

                // 显示加载状态
                document.getElementById('detailContent').innerHTML = '<p>加载中...</p>';
                openDrawer();

                if (cachedData) {
                    renderDetails(JSON.parse(cachedData));
                } else {
                    try {
                        const controller = new AbortController();
                        const timeoutId = setTimeout(() => {
                            controller.abort();
                            document.getElementById('detailContent').innerHTML = '<p style="color:red">请求超时，请检查网络</p>';
                            localStorage.removeItem(cacheKey);
                        }, 1000000);

                        const response = await fetch(`${baseUrl}/api/task-assignment/solution/detail/${taskId}`, {
                            signal: controller.signal,
                            method: 'GET',
                            headers: { 'Content-Type': 'application/json' },
                            mode: 'cors'
                        });
                        clearTimeout(timeoutId);

                        if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
                        const { data } = await response.json();
                        localStorage.setItem(cacheKey, JSON.stringify(data));
                        renderDetails(data);
                    } catch (error) {
                        document.getElementById('detailContent').innerHTML =
                            `<p style="color:red">加载失败：${error.message}</p>`;
                        localStorage.removeItem(cacheKey);
                    }
                }
            });
        });
    }

    // 抽屉交互
    function openDrawer(data) {
        const drawer = document.getElementById('drawer');
        drawer.classList.add('active');
        renderDetails(data);
    }

    function closeDrawer() {
        document.getElementById('drawer').classList.remove('active');
    }

    // 渲染详情
    function renderDetails(data) {
        const container = document.getElementById('detailContent');

        // 增强数据校验
        if (!data?.result?.length) {
            container.innerHTML = '<p style="color:red">数据加载中,请稍等...</p>';
            return;
        }

        container.innerHTML = `
            <div class="designer-list">
                ${data.result.map(designer => {
            const total = designer.designerCapacity || 0;
            const remain = designer.capacityRemain || 0;
            const used = total - remain;



            return `
                    <div class="designer-card">
                        <div class="designer-header" onclick="this.nextElementSibling.classList.toggle('active')">

                            <h4>设计师: ${designer.designerCode} 日额度: ${designer.dailyCapacity || 0} 超额上限: ${designer.designerCapacity || 0} 剩余产能: ${designer.capacityRemain || 0} 设计师职级: ${designer.designerLevel || '未知'}</h3>
                            <div class="capacity-progress">
                                <div class="capacity-filled"
                                     style="width: ${(used / total * 100 || 0)}%">
                                </div>

                            </div>
                                 <div>
                                <span>拥有的技能: ${designer.skill || '无指定技能'}</span>
                            </div>
                                     <div>
                                <span></span>
                            </div>
                               </br>
                              <div>
                                <span>偏好工单表达式: ${designer.preferTask || '无'}</span>
                            </div>
                            </br>
                                             <div>
                                 <span>偏好工单数量: ${designer.preferTaskCount || '0'}</span>
                            </div>
                               </br>
                                                       <div>
                                 <span>偏好工单额度: ${designer.preferTaskQuota || '0'}</span>
                            </div>
                               </br>
                                       <div>
                                <span>偏好技能表达式: ${designer.preferSkill || '无'}</span>
                            </div>

                               </br>
                                                                   <div>
                                 <span>偏好技能数量: ${designer.preferSkillCount || '0'}</span>
                            </div>
                               </br>
                                                                         <div>
                                 <span>偏好技能额度: ${designer.preferSkillQuota || '0'}</span>
                            </div>
                               </br>
                                       <div>
                                <span>全设计修改只分自己: ${designer.modifyDesignExclusive || '否'}</span>
                            </div>
                               </br>
                            <div>
                                <span>已用产能：${used}/${total}</span>
                            </div>
                               </br>
                              <div>
                                <span>在岗：${designer.onDuty | '不在岗' }</span>
                            </div>
                        </div>

                        <table class="task-table">
                            <thead>
                                <tr>
                                    <th>工单编码</th>
                                     <th>工单类型</th>
                                    <th>所需产能</th>
                                    <th>所需技能</th>
                                    <th>病例分配设计师</th>
                                     <th>该设计师的偏好工单</th>
                                      <th>该设计师的偏好技能</th>

                                </tr>
                            </thead>
                            <tbody>
                                ${(designer.assignedTask || []).map(task => `
                                <tr>
    <td>${task.taskCode || ''}</td>
    <td>${task.taskType || ''}</td>
    <td>${task.taskRequiredCapacityCount}个/${task.taskRequiredCapacityMinute}分钟</td>
    <td>${task.taskRequireSkill || '无'}</td>
    <td>${task.preferDesignerCode || '无'}</td>
    <td>${task.preferTask ? '是' : '否'}</td>
   <td>${task.preferSkill ? '是' : '否'}</td>
</tr>
                                `).join('')}
                            </tbody>
                        </table>
                    </div>
                    `;
        }).join('')}
            </div>
        `;
    }

    // 搜索过滤
    function filterDetails() {
        const keyword = document.getElementById('searchInput').value.toLowerCase();
        const cards = document.querySelectorAll('.designer-card');

        cards.forEach(card => {
            const text = card.textContent.toLowerCase();
            card.style.display = text.includes(keyword) ? 'block' : 'none';
        });
    }
</script>
</body>

</html>