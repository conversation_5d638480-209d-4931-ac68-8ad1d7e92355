package com.angelalign.tas.config;

import org.optaplanner.benchmark.api.PlannerBenchmarkFactory;
import org.optaplanner.benchmark.config.PlannerBenchmarkConfig;
import org.optaplanner.core.config.solver.SolverConfig;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

//@Configuration
//public class OptaBenchmarkConfiguration {
//    @Bean
//    public PlannerBenchmarkConfig benchmarkConfig(@Qualifier("scheduleConfig") SolverConfig config) {
//        return PlannerBenchmarkConfig.createFromSolverConfig(config);
//    }
//
//    @Bean
//    public PlannerBenchmarkFactory plannerBenchmarkFactory(PlannerBenchmarkConfig benchmarkConfig) {
//        return PlannerBenchmarkFactory.create(benchmarkConfig).;
//    }
//}
