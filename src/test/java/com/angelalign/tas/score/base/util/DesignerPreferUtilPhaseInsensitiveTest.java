package com.angelalign.tas.score.base.util;

import com.angelalign.tas.domain.assignment.Task;
import com.angelalign.tas.domain.assignment.TaskType;
import com.angelalign.tas.domain.assignment.DesignerConsumedQuotaTask;
import com.angelalign.tas.domain.parser.model.PreferTask;
import com.angelalign.tas.domain.parser.TaskAttributeParser;
import com.angelalign.tas.domain.parser.ConsumedQuotaTaskAttributeParser;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.mockito.MockedStatic;
import org.mockito.Mockito;

import java.util.List;
import java.util.ArrayList;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * 测试 PHASE_INSENSITIVE 表达式匹配功能
 */
public class DesignerPreferUtilPhaseInsensitiveTest {

    private Task mockTask;
    private TaskType mockTaskType;
    private DesignerConsumedQuotaTask mockConsumedTask;
    private List<PreferTask> preferTaskList;

    @BeforeEach
    void setUp() {
        // 创建模拟对象
        mockTask = mock(Task.class);
        mockTaskType = mock(TaskType.class);
        mockConsumedTask = mock(DesignerConsumedQuotaTask.class);
        
        // 设置任务类型
        when(mockTaskType.getCode()).thenReturn("abc");
        when(mockTask.getTaskType()).thenReturn(mockTaskType);
        when(mockConsumedTask.getTaskType()).thenReturn(mockTaskType);
        
        // 创建偏好任务列表
        preferTaskList = new ArrayList<>();
        preferTaskList.add(new PreferTask("abcPHASE_INSENSITIVE", 1.0));
    }

    @Test
    void testPhaseInsensitiveMatching_ForCurrentTask() {
        // 模拟任务技能代码包含匹配的前缀
        try (MockedStatic<TaskAttributeParser> mockedParser = Mockito.mockStatic(TaskAttributeParser.class)) {
            mockedParser.when(() -> TaskAttributeParser.getMegTag(any())).thenReturn(List.of());
            mockedParser.when(() -> TaskAttributeParser.getProductLine(any())).thenReturn("testLine");
            mockedParser.when(() -> TaskAttributeParser.getTaskRequireSkillCode(any()))
                    .thenReturn(List.of("abcNEW_PHASE", "otherSkill"));

            // 测试匹配 - 使用公共方法
            java.util.Set<Task> tasks = java.util.Set.of(mockTask);
            java.util.Set<Task> result = DesignerPreferUtil.getDesignerPreferTask(preferTaskList, tasks);

            assertFalse(result.isEmpty(), "应该匹配 abcPHASE_INSENSITIVE 表达式与 abcNEW_PHASE 技能代码");
        }
    }

    @Test
    void testPhaseInsensitiveMatching_ForConsumedTask() {
        // 模拟已消费任务的阶段类型
        try (MockedStatic<ConsumedQuotaTaskAttributeParser> mockedParser = 
                Mockito.mockStatic(ConsumedQuotaTaskAttributeParser.class)) {
            mockedParser.when(() -> ConsumedQuotaTaskAttributeParser.getMegTag(any())).thenReturn(List.of());
            mockedParser.when(() -> ConsumedQuotaTaskAttributeParser.getProductLine(any())).thenReturn("testLine");
            mockedParser.when(() -> ConsumedQuotaTaskAttributeParser.getPhaseType(any())).thenReturn("NEW_PHASE");

            // 测试匹配
            boolean result = DesignerPreferUtil.isConsumedPreferTask(mockConsumedTask, preferTaskList);
            
            assertTrue(result, "应该匹配 abcPHASE_INSENSITIVE 表达式与 abcNEW_PHASE 组合");
        }
    }

    @Test
    void testPhaseInsensitiveMatching_NoMatch() {
        // 模拟任务技能代码不包含匹配的前缀
        try (MockedStatic<TaskAttributeParser> mockedParser = Mockito.mockStatic(TaskAttributeParser.class)) {
            mockedParser.when(() -> TaskAttributeParser.getMegTag(any())).thenReturn(List.of());
            mockedParser.when(() -> TaskAttributeParser.getProductLine(any())).thenReturn("testLine");
            mockedParser.when(() -> TaskAttributeParser.getTaskRequireSkillCode(any()))
                    .thenReturn(List.of("xyzNEW_PHASE", "otherSkill"));

            // 测试不匹配 - 使用公共方法
            java.util.Set<Task> tasks = java.util.Set.of(mockTask);
            java.util.Set<Task> result = DesignerPreferUtil.getDesignerPreferTask(preferTaskList, tasks);

            assertTrue(result.isEmpty(), "不应该匹配 abcPHASE_INSENSITIVE 表达式与 xyzNEW_PHASE 技能代码");
        }
    }

    @Test
    void testNormalMatching_WithoutPhaseInsensitive() {
        // 创建不包含 PHASE_INSENSITIVE 的偏好任务
        List<PreferTask> normalPreferTaskList = new ArrayList<>();
        normalPreferTaskList.add(new PreferTask("abcNEW_PHASE", 1.0));

        try (MockedStatic<TaskAttributeParser> mockedParser = Mockito.mockStatic(TaskAttributeParser.class)) {
            mockedParser.when(() -> TaskAttributeParser.getMegTag(any())).thenReturn(List.of());
            mockedParser.when(() -> TaskAttributeParser.getProductLine(any())).thenReturn("testLine");
            mockedParser.when(() -> TaskAttributeParser.getTaskRequireSkillCode(any()))
                    .thenReturn(List.of("abcNEW_PHASE", "otherSkill"));

            // 测试正常匹配 - 使用公共方法
            java.util.Set<Task> tasks = java.util.Set.of(mockTask);
            java.util.Set<Task> result = DesignerPreferUtil.getDesignerPreferTask(normalPreferTaskList, tasks);

            assertFalse(result.isEmpty(), "应该正常匹配 abcNEW_PHASE 表达式与技能代码");
        }
    }
}
