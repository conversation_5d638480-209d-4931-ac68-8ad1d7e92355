FROM harbor.sh-sit.eainc.com/base/eclipse-temurin:17-jdk-focal
#版本
ARG v
#应用包

COPY build /opt/
RUN /opt/java/openjdk/bin/keytool -importcert -alias cas-ad-client -file /opt/cas-ad-client.crt -cacerts -storepass changeit -noprompt
RUN /opt/java/openjdk/bin/keytool -importcert -alias cas-adv -file /opt/cas-adv.cer -cacerts -storepass changeit -noprompt
RUN /opt/java/openjdk/bin/keytool -importcert -alias cas-prod -file /opt/cas-prod.cer -cacerts -storepass changeit -noprompt
RUN /opt/java/openjdk/bin/keytool -importcert -alias eainc_com_v3 -file /opt/eainc_com.crt -cacerts -storepass changeit -noprompt
ADD http://*************:8081/nexus/service/local/artifact/maven/redirect?r=releases&g=com.angelalign.taskassignment&a=tas&v=$v&p=jar /opt/tas.jar

EXPOSE 8080
CMD ["sh","/opt/start.sh"]
