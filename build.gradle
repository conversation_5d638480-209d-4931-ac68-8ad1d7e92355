buildscript {
    repositories {
        maven {
            allowInsecureProtocol true
            url "http://*************:8081/nexus/content/repositories/public/"
        }
    }
    dependencies {
        classpath "com.shidaits.gradle.plugin:gradle-version-plugin:1.0.12"
        classpath "com.shidaits.gradle.plugin:gradle-docker-plugin:1.0.72"
    }
}

plugins {
    id "org.springframework.boot" version "2.7.12"
    id "io.spring.dependency-management" version "1.0.11.RELEASE"
    id "java"
    id "maven-publish"
}

def optaplannerVersion = "9.44.0.Final"
def assertjVersion = "3.24.2"

springBoot {
    buildInfo()
}

group = "com.angelalign.taskassignment"
//archivesBaseName = "tas"
sourceCompatibility = "17"

repositories {
    // Local maven repository is required for libraries built locally with maven like development jhipster-bom.
    // mavenLocal()
    mavenCentral()
    // jhipster-needle-gradle-repositories - JHipster will add additional repositories
    /*本地中央仓库*/
    maven {
        allowInsecureProtocol true
        url "http://*************:8081/nexus/content/repositories/central/"
    }
    maven {
        allowInsecureProtocol true
        url "http://*************:8081/nexus/content/repositories/thirdparty/"
    }
    maven {
        allowInsecureProtocol true
        url "http://*************:8081/nexus/content/repositories/releases/"
    }
    maven {
        allowInsecureProtocol true
        url "http://*************:8081/nexus/content/repositories/snapshots/"
    }
    maven {
        allowInsecureProtocol true
        url 'http://*************:8081/nexus/content/repositories/aliyun/'
    }
    maven {
        allowInsecureProtocol true
        url 'http://*************:8081/nexus/content/repositories/public/'
    }
    mavenLocal()
    maven {
        url "https://repository.jboss.org/nexus/content/groups/public/"
        mavenContent {
            snapshotsOnly()
        }
    }
}

ext {
    imageName = "tas"
}

apply plugin: 'com.shidaits.plugin'
apply plugin: "com.shidaits.docker-plugin"

dependencies {
    testImplementation 'jakarta.activation:jakarta.activation-api:2.1.2'
    runtimeOnly 'com.sun.activation:jakarta.activation:2.0.1'
    testImplementation 'javax.xml.bind:jaxb-api:2.3.1'
    runtimeOnly 'com.sun.xml.bind:jaxb-impl:2.3.8'
    testImplementation 'jakarta.xml.bind:jakarta.xml.bind-api:4.0.0'
    runtimeOnly 'org.glassfish.jaxb:jaxb-runtime:4.0.0'
    testImplementation("org.optaplanner:optaplanner-test")
    testImplementation("org.optaplanner:optaplanner-benchmark")
    testImplementation "org.assertj:assertj-core:${assertjVersion}"
    testImplementation "io.rest-assured:rest-assured"
    testImplementation "org.awaitility:awaitility"
    testImplementation("org.springframework.boot:spring-boot-starter-test")
    testCompileOnly 'org.projectlombok:lombok:1.18.32'
    testAnnotationProcessor 'org.projectlombok:lombok:1.18.32'

    implementation "org.springframework.boot:spring-boot-starter-web"
    implementation "org.springframework.boot:spring-boot-starter-data-rest"
    implementation "org.springframework.boot:spring-boot-starter-data-jpa"
    implementation "org.apache.commons:commons-lang3"
    implementation "org.apache.commons:commons-collections4:4.4"
    implementation group: 'org.apache.httpcomponents', name: 'httpclient', version: '4.5.13'
    implementation 'com.github.ben-manes.caffeine:caffeine:3.1.8'
    compileOnly 'org.projectlombok:lombok:1.18.32'
    annotationProcessor 'org.projectlombok:lombok:1.18.32'
    implementation group: 'com.google.guava', name: 'guava', version: '33.2.1-jre'
    implementation platform("org.optaplanner:optaplanner-bom:${optaplannerVersion}")
    implementation "org.optaplanner:optaplanner-spring-boot-starter"
    implementation 'com.alibaba.boot:nacos-config-spring-boot-starter:0.2.11'
    implementation group: 'mysql', name: 'mysql-connector-java', version: '8.0.13'
    runtimeOnly "org.webjars:webjars-locator:0.37"
    runtimeOnly "org.webjars:bootstrap:4.3.1"
    runtimeOnly "org.webjars:font-awesome:5.11.2"
    runtimeOnly "org.webjars.npm:js-joda:1.11.0"
    // Micrometer Tracing 与 Logback 集成
    implementation 'io.micrometer:micrometer-tracing:1.4.1'
    implementation 'ch.qos.logback.contrib:logback-json-classic:0.1.5'
    implementation 'ch.qos.logback.contrib:logback-jackson:0.1.5'
    //redisson
    implementation 'org.redisson:redisson-spring-boot-starter:3.39.0'
    // https://mvnrepository.com/artifact/io.quarkus.gizmo/gizmo
    implementation group: 'io.quarkus.gizmo', name: 'gizmo', version: '1.9.0'
}

test {
    useJUnitPlatform()
    // Log the test execution results.
    testLogging {
        events "passed", "skipped", "failed"
    }
    jvmArgs += '-Djava.security.egd=file:/dev/./urandom -Xmx512m'
}

jar {
    enabled = false
}

publishing {
    println("publish to nexus")
    publications {
        bootJava(MavenPublication) {
            artifact tasks.named("bootJar")
        }
    }

    repositories {
        maven {
            //忽略https
            allowInsecureProtocol true
            name = 'tas' //short for "task assignment service"
            def releasesRepoUrl = "http://*************:8081/nexus/content/repositories/releases/"
            def snapshotsRepoUrl = "http://*************:8081/nexus/content/repositories/snapshots/"
            url = project.version.endsWith('SNAPSHOT') ? snapshotsRepoUrl : releasesRepoUrl
            credentials {
                username 'admin'
                password 'admin123'
            }
        }
    }
}
