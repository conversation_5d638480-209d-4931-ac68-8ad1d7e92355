<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设计师统计分析</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .drawer {
            position: fixed;
            top: 0;
            right: -100%;
            width: 80%;
            height: 100vh;
            background: white;
            box-shadow: -2px 0 10px rgba(0,0,0,0.1);
            transition: right 0.3s ease;
            z-index: 1050;
            overflow-y: auto;
        }
        
        .drawer.open {
            right: 0;
        }
        
        .drawer-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100vh;
            background: rgba(0,0,0,0.5);
            z-index: 1040;
            display: none;
        }
        
        .drawer-overlay.show {
            display: block;
        }
        
        .chart-container {
            position: relative;
            height: 400px;
            margin-bottom: 30px;
        }
        
        .drawer-header {
            background: #f8f9fa;
            padding: 20px;
            border-bottom: 1px solid #dee2e6;
            position: sticky;
            top: 0;
            z-index: 10;
        }
        
        .drawer-content {
            padding: 20px;
        }
        
        .charts-section {
            display: flex;
            flex-direction: column;
            gap: 30px;
        }
        
        .loading {
            text-align: center;
            padding: 50px;
        }
        
        .error {
            color: #dc3545;
            text-align: center;
            padding: 20px;
        }
        
        .stats-summary {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .stat-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
        }
        
        .stat-item:last-child {
            margin-bottom: 0;
        }
    </style>
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h4 class="mb-0">设计师统计查询</h4>
                    </div>
                    <div class="card-body">
                        <form id="queryForm">
                            <div class="mb-3">
                                <label for="problemId" class="form-label">问题ID</label>
                                <input type="text" class="form-control" id="problemId" placeholder="请输入问题ID" required>
                            </div>
                            <button type="submit" class="btn btn-primary w-100">查询统计数据</button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 抽屉遮罩 -->
    <div class="drawer-overlay" id="drawerOverlay"></div>

    <!-- 抽屉 -->
    <div class="drawer" id="statisticsDrawer">
        <div class="drawer-header">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="mb-0">设计师统计分析 - 问题ID: <span id="currentProblemId"></span></h5>
                <button type="button" class="btn-close" id="closeDrawer"></button>
            </div>
        </div>
        
        <div class="drawer-content">
            <div id="loadingSection" class="loading">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">加载中...</span>
                </div>
                <p class="mt-2">正在加载统计数据...</p>
            </div>
            
            <div id="errorSection" class="error" style="display: none;">
                <p>加载数据时出现错误，请稍后重试。</p>
            </div>
            
            <div id="contentSection" style="display: none;">
                <!-- 统计摘要 -->
                <div class="stats-summary">
                    <h6>统计摘要</h6>
                    <div class="stat-item">
                        <span>设计师总数:</span>
                        <strong id="totalDesigners">-</strong>
                    </div>
                    <div class="stat-item">
                        <span>工单总数:</span>
                        <strong id="totalTasks">-</strong>
                    </div>
                    <div class="stat-item">
                        <span>总额度:</span>
                        <strong id="totalQuota">-</strong>
                    </div>
                </div>
                
                <!-- 图表区域 -->
                <div class="charts-section">
                    <!-- 设计师额度统计图 -->
                    <div class="chart-section">
                        <h6>设计师占用额度统计</h6>
                        <div class="chart-container">
                            <canvas id="quotaChart"></canvas>
                        </div>
                    </div>
                    
                    <!-- 设计师工单数量统计图 -->
                    <div class="chart-section">
                        <h6>设计师工单数量统计</h6>
                        <div class="chart-container">
                            <canvas id="taskCountChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 全局变量
        let quotaChart = null;
        let taskCountChart = null;

        // DOM 元素
        const queryForm = document.getElementById('queryForm');
        const problemIdInput = document.getElementById('problemId');
        const drawer = document.getElementById('statisticsDrawer');
        const drawerOverlay = document.getElementById('drawerOverlay');
        const closeDrawerBtn = document.getElementById('closeDrawer');
        const currentProblemIdSpan = document.getElementById('currentProblemId');
        const loadingSection = document.getElementById('loadingSection');
        const errorSection = document.getElementById('errorSection');
        const contentSection = document.getElementById('contentSection');

        // 事件监听器
        queryForm.addEventListener('submit', handleFormSubmit);
        closeDrawerBtn.addEventListener('click', closeDrawer);
        drawerOverlay.addEventListener('click', closeDrawer);

        // URL参数处理
        window.addEventListener('load', function() {
            const urlParams = new URLSearchParams(window.location.search);
            const problemId = urlParams.get('problemId');
            if (problemId) {
                problemIdInput.value = problemId;
                handleFormSubmit(new Event('submit'));
            }
        });

        // 表单提交处理
        async function handleFormSubmit(event) {
            event.preventDefault();
            const problemId = problemIdInput.value.trim();
            
            if (!problemId) {
                alert('请输入问题ID');
                return;
            }

            openDrawer();
            currentProblemIdSpan.textContent = problemId;
            showLoading();
            
            try {
                const data = await fetchStatisticsData(problemId);
                displayStatistics(data);
            } catch (error) {
                console.error('获取数据失败:', error);
                showError();
            }
        }

        // 获取统计数据（模拟API调用）
        async function fetchStatisticsData(problemId) {
            // 模拟API延迟
            await new Promise(resolve => setTimeout(resolve, 1500));
            
            // 模拟数据 - 实际使用时替换为真实API调用
            return {
                problemId: problemId,
                designers: [
                    { name: '张三', quota: 120.5, taskCount: 15 },
                    { name: '李四', quota: 98.2, taskCount: 12 },
                    { name: '王五', quota: 156.8, taskCount: 20 },
                    { name: '赵六', quota: 87.3, taskCount: 11 },
                    { name: '钱七', quota: 134.6, taskCount: 17 },
                    { name: '孙八', quota: 76.9, taskCount: 9 },
                    { name: '周九', quota: 145.2, taskCount: 18 },
                    { name: '吴十', quota: 92.4, taskCount: 13 }
                ],
                summary: {
                    totalDesigners: 8,
                    totalTasks: 115,
                    totalQuota: 911.9
                }
            };
        }

        // 显示统计数据
        function displayStatistics(data) {
            // 更新摘要信息
            document.getElementById('totalDesigners').textContent = data.summary.totalDesigners;
            document.getElementById('totalTasks').textContent = data.summary.totalTasks;
            document.getElementById('totalQuota').textContent = data.summary.totalQuota.toFixed(1);

            // 创建图表
            createQuotaChart(data.designers);
            createTaskCountChart(data.designers);

            showContent();
        }

        // 创建额度统计图
        function createQuotaChart(designers) {
            const ctx = document.getElementById('quotaChart').getContext('2d');
            
            if (quotaChart) {
                quotaChart.destroy();
            }

            quotaChart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: designers.map(d => d.name),
                    datasets: [{
                        label: '占用额度',
                        data: designers.map(d => d.quota),
                        backgroundColor: 'rgba(54, 162, 235, 0.8)',
                        borderColor: 'rgba(54, 162, 235, 1)',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: '设计师占用额度分布'
                        },
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: '额度'
                            }
                        },
                        x: {
                            title: {
                                display: true,
                                text: '设计师'
                            }
                        }
                    }
                }
            });
        }

        // 创建工单数量统计图
        function createTaskCountChart(designers) {
            const ctx = document.getElementById('taskCountChart').getContext('2d');
            
            if (taskCountChart) {
                taskCountChart.destroy();
            }

            taskCountChart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: designers.map(d => d.name),
                    datasets: [{
                        label: '工单数量',
                        data: designers.map(d => d.taskCount),
                        backgroundColor: 'rgba(255, 99, 132, 0.8)',
                        borderColor: 'rgba(255, 99, 132, 1)',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: '设计师工单数量分布'
                        },
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: '工单数量'
                            }
                        },
                        x: {
                            title: {
                                display: true,
                                text: '设计师'
                            }
                        }
                    }
                }
            });
        }

        // 抽屉控制函数
        function openDrawer() {
            drawer.classList.add('open');
            drawerOverlay.classList.add('show');
            document.body.style.overflow = 'hidden';
        }

        function closeDrawer() {
            drawer.classList.remove('open');
            drawerOverlay.classList.remove('show');
            document.body.style.overflow = '';
        }

        // 显示状态控制
        function showLoading() {
            loadingSection.style.display = 'block';
            errorSection.style.display = 'none';
            contentSection.style.display = 'none';
        }

        function showError() {
            loadingSection.style.display = 'none';
            errorSection.style.display = 'block';
            contentSection.style.display = 'none';
        }

        function showContent() {
            loadingSection.style.display = 'none';
            errorSection.style.display = 'none';
            contentSection.style.display = 'block';
        }

        // ESC键关闭抽屉
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape' && drawer.classList.contains('open')) {
                closeDrawer();
            }
        });
    </script>
</body>
</html>
